@echo off
echo ========================================
echo LinkEye Release Build Script
echo ========================================

echo.
echo 1. Cleaning previous builds...
call gradlew clean

echo.
echo 2. Building Release APK...
call gradlew assembleRelease

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo BUILD SUCCESSFUL!
    echo ========================================
    echo.
    echo Release APK location:
    echo app\build\outputs\apk\release\app-release.apk
    echo.
    echo Verifying APK signature...
    jarsigner -verify -verbose -certs app\build\outputs\apk\release\app-release.apk | findstr "jar 已验证"
    echo.
    echo APK size:
    for %%A in (app\build\outputs\apk\release\app-release.apk) do echo %%~zA bytes
    echo.
    echo Build completed successfully!
) else (
    echo.
    echo ========================================
    echo BUILD FAILED!
    echo ========================================
    echo Please check the error messages above.
)

pause
