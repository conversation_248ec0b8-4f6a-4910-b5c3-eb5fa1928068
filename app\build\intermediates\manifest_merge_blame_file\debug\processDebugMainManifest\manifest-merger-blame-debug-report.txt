1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.linkeye.debug"
4    android:versionCode="1"
5    android:versionName="1.0.0-debug" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:5:5-67
11-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
12-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:6:5-80
12-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:6:22-77
13    <uses-permission android:name="android.permission.RECORD_AUDIO" />
13-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:7:5-71
13-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:7:22-68
14
15    <uses-feature
15-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:8:5-87
16        android:name="android.software.leanback"
16-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:8:19-59
17        android:required="false" />
17-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:8:60-84
18    <uses-feature
18-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:9:5-90
19        android:name="android.hardware.touchscreen"
19-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:9:19-62
20        android:required="false" />
20-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:9:63-87
21
22    <application
22-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:11:5-28:19
23        android:allowBackup="true"
23-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:13:9-35
24        android:debuggable="true"
25        android:extractNativeLibs="true"
26        android:hardwareAccelerated="true"
26-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:14:9-43
27        android:label="link-eye"
27-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:12:9-33
28        android:networkSecurityConfig="@xml/network_security_config"
28-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:15:9-69
29        android:testOnly="true"
30        android:theme="@style/AppTheme"
30-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:17:9-40
31        android:usesCleartextTraffic="true" >
31-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:16:9-44
32        <activity
32-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:18:9-27:20
33            android:name="com.example.linkeye.MainActivity"
33-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:19:13-41
34            android:configChanges="keyboardHidden|orientation|screenSize"
34-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:21:13-74
35            android:exported="true" >
35-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:20:13-36
36            <intent-filter>
36-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:22:13-26:29
37                <action android:name="android.intent.action.MAIN" />
37-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:23:17-69
37-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:23:25-66
38
39                <category android:name="android.intent.category.LAUNCHER" />
39-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:24:17-77
39-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:24:27-74
40                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
40-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:25:17-86
40-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:25:27-83
41            </intent-filter>
42        </activity>
43    </application>
44
45</manifest>
